微信购物小程序完整代码实现
一、项目结构

shopping-miniprogram/
├── app.js               // 全局入口
├── app.json             // 全局配置
├── app.wxss             // 全局样式
├── pages/
│   ├── index/           // 首页
│   ├── category/        // 商品分类
│   ├── goods/           // 商品详情
│   ├── cart/            // 购物车
│   └── user/            // 个人中心
└── utils/
    └── api.js           // 接口封装
二、全局配置文件
app.json

{
  "pages": [
    "pages/index/index",
    "pages/category/category",
    "pages/goods/goods",
    "pages/cart/cart",
    "pages/user/user"
  ],
  "window": {
    "navigationBarTitleText": "零食购物",
    "navigationBarBackgroundColor": "#f8f8f8",
    "navigationBarTextStyle": "black"
  },
  "tabBar": {
    "color": "#666",
    "selectedColor": "#ff4400",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "/images/tab/home.png",
        "selectedIconPath": "/images/tab/home-active.png"
      },
      {
        "pagePath": "pages/category/category",
        "text": "分类",
        "iconPath": "/images/tab/category.png",
        "selectedIconPath": "/images/tab/category-active.png"
      },
      {
        "pagePath": "pages/cart/cart",
        "text": "购物车",
        "iconPath": "/images/tab/cart.png",
        "selectedIconPath": "/images/tab/cart-active.png"
      },
      {
        "pagePath": "pages/user/user",
        "text": "我的",
        "iconPath": "/images/tab/user.png",
        "selectedIconPath": "/images/tab/user-active.png"
      }
    ]
  }
}
app.js

App({
  onLaunch() {
    // 初始化本地存储购物车
    if (!wx.getStorageSync('cartList')) {
      wx.setStorageSync('cartList', [])
    }
    
    // 微信授权登录
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          wx.getUserInfo({
            success: userRes => {
              this.globalData.userInfo = userRes.userInfo
            }
          })
        }
      }
    })
  },
  globalData: {
    userInfo: null,
    baseUrl: 'https://api.example.com' // 后端接口地址
  }
})
二、核心页面实现
1. 首页（pages/index/index）
index.wxml

<view class="container">
  <!-- 轮播图 -->
  <swiper indicator-dots autoplay interval="3000">
    <block wx:for="{{bannerList}}" wx:key="id">
      <swiper-item>
        <image src="{{item.imgUrl}}" mode="widthFix" bindtap="goGoodsDetail" data-id="{{item.goodsId}}"></image>
      </swiper-item>
    </block>
  </swiper>
  <!-- 分类入口 -->
  <view class="category-entry">
    <block wx:for="{{categoryList}}" wx:key="id">
      <view class="category-item" bindtap="goCategory" data-id="{{item.id}}">
        <image src="{{item.icon}}" mode="widthFix"></image>
        <text>{{item.name}}</text>
      </view>
    </block>
  </view>
  <!-- 热门商品 -->
  <view class="hot-title">热门推荐</view>
  <view class="goods-list">
    <block wx:for="{{hotGoodsList}}" wx:key="id">
      <view class="goods-item" bindtap="goGoodsDetail" data-id="{{item.id}}">
        <image src="{{item.imgUrl}}" mode="widthFix"></image>
        <text class="goods-name">{{item.name}}</text>
        <text class="goods-price">¥{{item.price}}</text>
      </view>
    </block>
  </view>
</view>
index.js

const app = getApp()
const api = require('../../utils/api.js')
Page({
  data: {
    bannerList: [],
    categoryList: [],
    hotGoodsList: []
  },
  onLoad() {
    this.loadHomeData()
  },
  loadHomeData() {
    // 加载轮播图
    api.get('/banner').then(res => {
      this.setData({ bannerList: res.data })
    })
    
    // 加载分类
    api.get('/category').then(res => {
      this.setData({ categoryList: res.data })
    })
    
    // 加载热门商品
    api.get('/goods/hot').then(res => {
      this.setData({ hotGoodsList: res.data })
    })
  },
  goGoodsDetail(e) {
    wx.navigateTo({
      url: `/pages/goods/goods?id=${e.currentTarget.dataset.id}`
    })
  },
  goCategory(e) {
    wx.navigateTo({
      url: `/pages/category/category?id=${e.currentTarget.dataset.id}`
    })
  }
})
index.wxss

.container {
  background-color: #f5f5f5;
}
swiper {
  width: 100%;
  height: 300rpx;
}
swiper image {
  width: 100%;
  height: 100%;
}
.category-entry {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
}
.category-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10rpx 0;
}
.category-item image {
  width: 80rpx;
  height: 80rpx;
}
.goods-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
}
.goods-item {
  width: 48%;
  margin: 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 10rpx;
}
.goods-item image {
  width: 100%;
  height: 200rpx;
}
.goods-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.goods-price {
  color: #ff4400;
  font-weight: bold;
}
2. 商品详情页（pages/goods/goods）
goods.wxml

<view class="goods-container">
  <!-- 商品图片 -->
  <image src="{{goodsInfo.imgUrl}}" mode="widthFix"></image>
  <!-- 商品信息 -->
  <view class="goods-info">
    <text class="goods-title">{{goodsInfo.name}}</text>
    <text class="goods-price">¥{{goodsInfo.price}}</text>
    <text class="goods-stock">库存: {{goodsInfo.stock}}件</text>
  </view>
  <!-- 规格选择 -->
  <view class="spec-container">
    <text class="spec-title">选择规格:</text>
    <view class="spec-list">
      <block wx:for="{{goodsInfo.specs}}" wx:key="id">
        <view class="spec-item {{item.selected ? 'spec-selected' : ''}}" bindtap="selectSpec" data-id="{{item.id}}">
          {{item.name}}
        </view>
      </block>
    </view>
  </view>
  <!-- 数量选择 -->
  <view class="count-container">
    <text>数量:</text>
    <view class="count-control">
      <button bindtap="decreaseCount" disabled="{{count <= 1}}">-</button>
      <text>{{count}}</text>
      <button bindtap="increaseCount" disabled="{{count >= goodsInfo.stock}}">+</button>
    </view>
  </view>
  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <button class="cart-btn" bindtap="addToCart">加入购物车</button>
    <button class="buy-btn" bindtap="buyNow">立即购买</button>
  </view>
</view>
goods.js

const app = getApp()
const api = require('../../utils/api.js')
Page({
  data: {
    goodsId: '',
    goodsInfo: {},
    count: 1,
    selectedSpecId: ''
  },
  onLoad(options) {
    this.setData({ goodsId: options.id })
    this.loadGoodsDetail()
  },
  loadGoodsDetail() {
    api.get(`/goods/${this.data.goodsId}`).then(res => {
      // 初始化规格选中状态
      res.data.specs.forEach(spec => {
        spec.selected = false
      })
      if (res.data.specs.length > 0) {
        res.data.specs[0].selected = true
        this.setData({ selectedSpecId: res.data.specs[0].id })
      }
      this.setData({ goodsInfo: res.data })
    })
  },
  selectSpec(e) {
    const specId = e.currentTarget.dataset.id
    const newSpecs = this.data.goodsInfo.specs.map(spec => {
      spec.selected = spec.id === specId
      return spec
    })
    this.setData({
      goodsInfo: { ...this.data.goodsInfo, specs: newSpecs },
      selectedSpecId: specId
    })
  },
  decreaseCount() {
    this.setData({ count: this.data.count - 1 })
  },
  increaseCount() {
    this.setData({ count: this.data.count + 1 })
  },
  addToCart() {
    const cartItem = {
      goodsId: this.data.goodsId,
      name: this.data.goodsInfo.name,
      price: this.data.goodsInfo.price,
      imgUrl: this.data.goodsInfo.imgUrl,
      count: this.data.count,
      specId: this.data.selectedSpecId,
      specName: this.data.goodsInfo.specs.find(s => s.id === this.data.selectedSpecId).name
    }
    // 从缓存获取购物车数据
    let cartList = wx.getStorageSync('cartList') || []
    const existingIndex = cartList.findIndex(item => 
      item.goodsId === cartItem.goodsId && item.specId === cartItem.specId
    )
    if (existingIndex > -1) {
      cartList[existingIndex].count += cartItem.count
    } else {
      cartList.push(cartItem)
    }
    // 保存到缓存
    wx.setStorageSync('cartList', cartList)
    wx.showToast({ title: '加入购物车成功' })
  },
  buyNow() {
    // 跳转到结算页逻辑
    wx.navigateTo({
      url: `/pages/checkout/checkout?goodsId=${this.data.goodsId}&count=${this.data.count}&specId=${this.data.selectedSpecId}`
    })
  }
})
3. 购物车页面（pages/cart/cart）
cart.wxml

<view class="cart-container">
  <block wx:if="{{cartList.length > 0}}">
    <view class="cart-list">
      <block wx:for="{{cartList}}" wx:key="index">
        <view class="cart-item">
          <checkbox checked="{{item.selected}}" bindchange="selectItem" data-index="{{index}}"></checkbox>
          <image src="{{item.imgUrl}}" mode="widthFix"></image>
          <view class="cart-info">
            <text class="cart-name">{{item.name}}</text>
            <text class="cart-spec">规格: {{item.specName}}</text>
            <view class="cart-price-count">
              <text class="cart-price">¥{{item.price}}</text>
              <view class="cart-count">
                <button bindtap="decreaseCount" data-index="{{index}}" disabled="{{item.count <= 1}}">-</button>
                <text>{{item.count}}</text>
                <button bindtap="increaseCount" data-index="{{index}}">+</button>
              </view>
            </view>
          </view>
          <button class="delete-btn" bindtap="deleteItem" data-index="{{index}}">删除</button>
        </view>
      </block>
    </view>
    <!-- 结算栏 -->
    <view class="checkout-bar">
      <checkbox checked="{{allSelected}}" bindchange="selectAll">全选</checkbox>
      <view class="total-info">
        <text>合计: ¥{{totalPrice}}</text>
        <button bindtap="goCheckout">结算({{selectedCount}})</button>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="empty-cart">
      <image src="/images/empty-cart.png" mode="widthFix"></image>
      <text>购物车是空的</text>
      <button bindtap="goHome">去逛逛</button>
    </view>
  </block>
</view>
cart.js

const app = getApp()
Page({
  data: {
    cartList: [],
    allSelected: false,
    totalPrice: 0,
    selectedCount: 0
  },
  onShow() {
    this.loadCartData()
  },
  loadCartData() {
    const cartList = wx.getStorageSync('cartList') || []
    this.setData({ cartList }, () => {
      this.calculateTotal()
    })
  },
  selectItem(e) {
    const index = e.currentTarget.dataset.index
    const newCartList = [...this.data.cartList]
    newCartList[index].selected = !newCartList[index].selected
    this.setData({ cartList: newCartList }, () => {
      this.checkAllSelected()
      this.calculateTotal()
    })
  },
  selectAll(e) {
    const allSelected = e.detail.value[0] === 'true'
    const newCartList = this.data.cartList.map(item => ({
      ...item,
      selected: allSelected
    }))
    this.setData({ 
      cartList: newCartList,
      allSelected
    }, () => {
      this.calculateTotal()
    })
  },
  checkAllSelected() {
    const allSelected = this.data.cartList.every(item => item.selected)
    this.setData({ allSelected })
  },
  calculateTotal() {
    let totalPrice = 0
    let selectedCount = 0
    this.data.cartList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price * item.count
        selectedCount += item.count
      }
    })
    this.setData({ totalPrice, selectedCount })
  },
  decreaseCount(e) {
    const index = e.currentTarget.dataset.index
    const newCartList = [...this.data.cartList]
    newCartList[index].count -= 1
    this.setData({ cartList: newCartList }, () => {
      wx.setStorageSync('cartList', newCartList)
      this.calculateTotal()
    })
  },
  increaseCount(e) {
    const index = e.currentTarget.dataset.index
    const newCartList = [...this.data.cartList]
    newCartList[index].count += 1
    this.setData({ cartList: newCartList }, () => {
      wx.setStorageSync('cartList', newCartList)
      this.calculateTotal()
    })
  },
  deleteItem(e) {
    const index = e.currentTarget.dataset.index
    wx.showModal({
      title: '提示',
      content: '确定要删除吗？',
      success: res => {
        if (res.confirm) {
          const newCartList = [...this.data.cartList]
          newCartList.splice(index, 1)
          this.setData({ cartList: newCartList }, () => {
            wx.setStorageSync('cartList', newCartList)
            this.calculateTotal()
          })
        }
      }
    })
  },
  goCheckout() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({ url: '/pages/checkout/checkout' })
  },
  goHome() {
    wx.switchTab({ url: '/pages/index/index' })
  }
})
三、工具类封装（utils/api.js）

const app = getApp()
/**
 * 接口请求封装
 * @param {string} url 接口路径
 * @param {object} data 请求参数
 * @param {string} method 请求方法
 */
function request(url, data = {}, method = 'GET') {
  return new Promise((resolve, reject) => {
    wx.request({
      url: app.globalData.baseUrl + url,
      data,
      method,
      header: {
        'content-type': 'application/json',
        'token': wx.getStorageSync('token') || ''
      },
      success: res => {
        if (res.data.code === 200) {
          resolve(res.data)
        } else if (res.data.code === 401) {
          // 登录失效处理
          wx.removeStorageSync('token')
          wx.navigateTo({ url: '/pages/login/login' })
          reject(res.data)
        } else {
          wx.showToast({
            title: res.data.msg || '请求失败',
            icon: 'none'
          })
          reject(res.data)
        }
      },
      fail: err => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}
// 快捷方法
module.exports = {
  get: (url, data) => request(url, data, 'GET'),
  post: (url, data) => request(url, data, 'POST'),
  put: (url, data) => request(url, data, 'PUT'),
  delete: (url, data) => request(url, data, 'DELETE')
}
四、配置说明
1.	接口配置：需在 app.js 中修改 baseUrl 为实际后端接口地址
2.	图片资源：需自行准备相关图片资源（轮播图、商品图等）
3.	权限配置：在 app.json 中配置需要的权限，如：

"permission": {
  "scope.userLocation": {
    "desc": "用于获取您的位置信息以便配送"
  }
}
1.	合法域名配置：在微信开发者工具中配置 request 合法域名（需与 baseUrl 一致）
五、注意事项
1.	本代码为前端示例，实际生产环境需配合后端接口使用
2.	支付功能需在微信商户平台申请开通，并对接微信支付 API
3.	需在微信公众平台配置小程序的业务域名、服务器域名等
4.	完整实现需补充登录页、结算页、订单页等扩展页面
5.	正式上线前需通过微信小程序审核，确保符合平台规范
可根据实际业务需求扩展功能模块，如优惠券、评价系统、物流跟踪等功能。代码采用模块化设计，便于维护和扩展。
（注：文档部分内容可能由 AI 生成）
